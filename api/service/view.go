package service

import (
	"context"

	"hotel/api/protocol"
	"hotel/common/bff"
	"hotel/common/i18n"
	contentService "hotel/content/service"
)

type ViewService struct {
	starlingSrv *contentService.StarlingService
}

func NewViewService() *ViewService {
	return &ViewService{}
}
func (s *ViewService) Name() string {
	return "view"
}

// AdminHomepage
// @desc: 获取首页的前端展示信息
// @path: /adminHomepage
// @tags: admin.hotelbyte.com
// @auth: required
func (s *ViewService) AdminHomepage(ctx context.Context, req *protocol.AdminHomepageReq) (*protocol.AdminHomepageResp, error) {
	return &protocol.AdminHomepageResp{
		DownloadButtonURLs: map[string]string{
			"batch-invitation-template-excel": "",
		},
		StarlingMap: i18n.Map{},
	}, nil
}

// InternalHomepage
// @desc: 获取内部管理前端的展示信息
// @path: /internalHomepage
// @tags: internal
// @auth: required
func (s *ViewService) InternalHomepage(ctx context.Context, req *protocol.InternalHomepageReq) (*protocol.InternalHomepageResp, error) {
	// 加载 starling 多语言数据
	var (
		starlingMap i18n.Map
		err         error
	)
	if s.starlingSrv != nil {
		starlingMap, err = s.starlingSrv.FindByApplication(ctx, 0, "admin-fe")
		if err != nil {
			// 如果加载失败，记录错误但不影响主要功能
			// TODO: 添加日志记录
			starlingMap = make(i18n.Map)
		}
	} else {
		// 如果没有数据库连接（如测试环境），使用空的 starlingMap
		starlingMap = make(i18n.Map)
	}

	return &protocol.InternalHomepageResp{
		DownloadButtonURLs: map[string]string{
			"batch-invitation-template-excel": "",
			"user-import-template-excel":      "",
		},
		// 新版本菜单路由结构
		MenuRoutes: []*bff.MenuRoute{
			// Dashboard 仪表盘菜单
			{
				ID:        "dashboard",
				Path:      "/dashboard",
				Component: "/layouts/default",
				Name:      "Dashboard",
				Title: i18n.I18N{
					Zh: "仪表盘",
					En: "Dashboard",
					Ar: "لوحة القيادة",
				},
				Icon: "dashboard",
				Meta: &bff.MenuRouteMeta{
					RequiresAuth: true,
					Roles:        []string{"admin", "manager"},
				},
				Children: []*bff.MenuRoute{
					{
						ID:        "console",
						Path:      "console",
						Component: "/views/dashboard/console",
						Name:      "Console",
						Title: i18n.I18N{
							Zh: "工作台",
							En: "Console",
							Ar: "وحدة التحكم",
						},
						Meta: &bff.MenuRouteMeta{
							KeepAlive: true,
						},
					},
				},
			},
			// Booking 预订菜单
			{
				ID:        "booking",
				Path:      "/booking",
				Component: "/layouts/default",
				Name:      "Booking",
				Title: i18n.I18N{
					Zh: "预订",
					En: "Booking",
					Ar: "الحجز",
				},
				Icon: "booking",
				Meta: &bff.MenuRouteMeta{
					RequiresAuth: true,
					Roles:        []string{"admin", "manager", "user"},
				},
				Children: []*bff.MenuRoute{
					{
						ID:        "hotel-search",
						Path:      "hotel-search",
						Component: "/views/booking/hotel-search/index",
						Name:      "HotelSearch",
						Title: i18n.I18N{
							Zh: "酒店搜索",
							En: "Hotel Search",
							Ar: "البحث عن الفنادق",
						},
						Meta: &bff.MenuRouteMeta{
							KeepAlive: true,
						},
					},
					{
						ID:        "hotel-detail",
						Path:      "hotel-detail",
						Component: "/views/booking/hotel-search/hotel-detail",
						Name:      "HotelStaticDetail",
						Title: i18n.I18N{
							Zh: "酒店详情",
							En: "Hotel Detail",
							Ar: "تفاصيل الفندق",
						},
						Meta: &bff.MenuRouteMeta{
							KeepAlive:  true,
							IsHide:     true,
							ActivePath: "/booking/hotel-search",
						},
					},
					{
						ID:        "hotel-book",
						Path:      "hotel-search/book",
						Component: "/views/booking/hotel-search/hotel-book",
						Name:      "HotelBook",
						Title: i18n.I18N{
							Zh: "酒店预订",
							En: "Hotel Booking",
							Ar: "حجز الفندق",
						},
						Meta: &bff.MenuRouteMeta{
							KeepAlive:  false,
							IsHide:     true,
							ActivePath: "/booking/hotel-search",
						},
					},
					{
						ID:        "orders",
						Path:      "orders",
						Component: "/views/booking/order-management",
						Name:      "Orders",
						Title: i18n.I18N{
							Zh: "订单管理",
							En: "Orders",
							Ar: "الطلبات",
						},
						Meta: &bff.MenuRouteMeta{
							KeepAlive: true,
						},
					},
					{
						ID:        "order-detail",
						Path:      "orders/detail/:orderId",
						Component: "/views/booking/order-management/order-detail",
						Name:      "OrderDetail",
						Title: i18n.I18N{
							Zh: "订单详情",
							En: "Order Detail",
							Ar: "تفاصيل الطلب",
						},
						Meta: &bff.MenuRouteMeta{
							KeepAlive:  false,
							IsHide:     true,
							ActivePath: "/booking/orders",
						},
					},
					{
						ID:        "analytics",
						Path:      "analytics",
						Component: "/views/booking/analytics",
						Name:      "BookingAnalytics",
						Title: i18n.I18N{
							Zh: "预订统计",
							En: "Booking Analytics",
							Ar: "إحصائيات الحجز",
						},
						Meta: &bff.MenuRouteMeta{
							KeepAlive: true,
						},
					},
				},
			},
			{
				ID:        "users",
				Path:      "/users",
				Component: "/layouts/default",
				Name:      "Users",
				Title: i18n.I18N{
					Zh: "用户管理",
					En: "User Management",
					Ar: "إدارة المستخدمين",
				},
				Icon: "user",
				Meta: &bff.MenuRouteMeta{
					RequiresAuth: true,
					Roles:        []string{"admin", "manager"},
				},
				Children: []*bff.MenuRoute{
					{
						ID:        "tenant-users",
						Path:      "tenant-users",
						Component: "/views/users/tenant-users",
						Name:      "TenantUsers",
						Title: i18n.I18N{
							Zh: "租户用户",
							En: "Tenant Users",
							Ar: "مستخدمو المستأجر",
						},
						Meta: &bff.MenuRouteMeta{
							KeepAlive:   true,
							Permissions: []string{"user:tenant:read"},
						},
					},
					{
						ID:        "customer-users",
						Path:      "customer-users",
						Component: "/views/users/customer-users",
						Name:      "CustomerUsers",
						Title: i18n.I18N{
							Zh: "客户用户",
							En: "Customer Users",
							Ar: "مستخدمو العملاء",
						},
						Meta: &bff.MenuRouteMeta{
							KeepAlive:   true,
							Permissions: []string{"user:customer:read"},
						},
					},
					{
						ID:        "platform-users",
						Path:      "platform-users",
						Component: "/views/users/platform-users",
						Name:      "PlatformUsers",
						Title: i18n.I18N{
							Zh: "平台用户",
							En: "Platform Users",
							Ar: "مستخدمو المنصة",
						},
						Meta: &bff.MenuRouteMeta{
							KeepAlive:   true,
							Permissions: []string{"user:platform:read"},
						},
					},
				},
			},
			// 用户编辑路由（隐藏路由，通过编辑按钮访问）
			{
				ID:        "user-edit",
				Path:      "/users/user-edit/:id",
				Component: "/views/users/user-edit",
				Name:      "UserEdit",
				Title: i18n.I18N{
					Zh: "编辑用户",
					En: "Edit User",
					Ar: "تحرير المستخدم",
				},
				Meta: &bff.MenuRouteMeta{
					RequiresAuth: true,
					KeepAlive:    true,
					IsHide:       true, // 隐藏路由，不在菜单中显示
					Permissions:  []string{"user:edit"},
					ActivePath:   "/users", // 激活父级菜单
				},
			},
			{
				ID:        "entity",
				Path:      "/entity",
				Component: "/layouts/default",
				Name:      "Entity",
				Title: i18n.I18N{
					Zh: "实体管理",
					En: "Entity Management",
					Ar: "إدارة الكيانات",
				},
				Icon: "entity",
				Meta: &bff.MenuRouteMeta{
					RequiresAuth: true,
				},
				Children: []*bff.MenuRoute{
					{
						ID:        "entity-list",
						Path:      "entity-list",
						Component: "/views/entity/entity-list",
						Name:      "EntityList",
						Title: i18n.I18N{
							Zh: "实体列表",
							En: "Entity List",
							Ar: "قائمة الكيانات",
						},
						Meta: &bff.MenuRouteMeta{
							KeepAlive:   true,
							Permissions: []string{"entity:read"},
						},
					},
				},
			},
			// 实体编辑路由（隐藏路由，通过编辑按钮访问）
			{
				ID:        "entity-edit",
				Path:      "/entity/entity-edit/:id",
				Component: "/views/entity/entity-edit",
				Name:      "EntityEdit",
				Title: i18n.I18N{
					Zh: "编辑实体",
					En: "Edit Entity",
					Ar: "تحرير الكيان",
				},
				Meta: &bff.MenuRouteMeta{
					RequiresAuth: true,
					KeepAlive:    true,
					IsHide:       true, // 隐藏路由，不在菜单中显示
					Permissions:  []string{"entity:edit"},
					ActivePath:   "/entity", // 激活父级菜单
				},
			},
			{
				ID:        "system-management",
				Path:      "/system-management",
				Component: "/layouts/default",
				Name:      "SystemManagement",
				Title: i18n.I18N{
					Zh: "系统管理",
					En: "System Management",
					Ar: "إدارة النظام",
				},
				Icon: "setting",
				Meta: &bff.MenuRouteMeta{
					RequiresAuth: true,
					Roles:        []string{"admin"},
				},
				Children: []*bff.MenuRoute{
					{
						ID:        "user-center",
						Path:      "user-center",
						Component: "/views/system-management/user-center",
						Name:      "UserCenter",
						Title: i18n.I18N{
							Zh: "个人中心",
							En: "User Center",
							Ar: "مركز المستخدم",
						},
						Meta: &bff.MenuRouteMeta{
							KeepAlive: true,
						},
					},
					{
						ID:        "starling-config",
						Path:      "starling-config",
						Component: "/views/system/starling",
						Name:      "StarlingConfig",
						Title: i18n.I18N{
							Zh: "多语言配置",
							En: "Multilingual Configuration",
							Ar: "تكوين متعدد اللغات",
						},
						Meta: &bff.MenuRouteMeta{
							KeepAlive:   true,
							Permissions: []string{"system:starling:read"},
						},
					},
					{
						ID:        "logs-viewer",
						Path:      "logs",
						Component: "/views/system/logs",
						Name:      "LogsViewer",
						Title: i18n.I18N{
							Zh: "日志查看器",
							En: "Logs Viewer",
							Ar: "عارض السجلات",
						},
						Meta: &bff.MenuRouteMeta{
							KeepAlive:   true,
							Permissions: []string{"system:logs:read"},
						},
					},
				},
			},
		},
		StarlingMap: starlingMap,
	}, nil
}
