{"httpMsg": {"unauthorized": "Unauthorized access, please login again", "forbidden": "Access to this resource is forbidden", "notFound": "The requested resource does not exist", "methodNotAllowed": "Request method not allowed", "requestTimeout": "Request timeout, please try again later", "internalServerError": "Internal server error, please try again later", "badGateway": "Bad gateway error, please try again later", "serviceUnavailable": "Service temporarily unavailable, please try again later", "gatewayTimeout": "Gateway timeout, please try again later", "requestCancelled": "Request cancelled", "networkError": "Network connection error, please check your connection", "requestFailed": "Request failed", "requestConfigError": "Request configuration error"}, "topBar": {"search": {"title": "Search"}, "user": {"userCenter": "User center", "docs": "Document", "github": "<PERSON><PERSON><PERSON>", "lockScreen": "Lock screen", "logout": "Log out"}, "guide": {"title": "Click here to view", "theme": "Theme style", "menu": "Open top menu", "description": "More configurations"}}, "common": {"tips": "Prompt", "cancel": "Cancel", "confirm": "Confirm", "logOutTips": "Do you want to log out?"}, "search": {"placeholder": "Search page", "historyTitle": "Search history", "switchKeydown": "Navigate", "selectKeydown": "Select"}, "setting": {"menuType": {"title": "<PERSON><PERSON>out", "list": ["Vertical", "Horizontal", "Mixed", "Dual"]}, "theme": {"title": "Theme Style", "list": ["Light", "Dark", "System"]}, "menu": {"title": "Menu Style"}, "color": {"title": "Theme Color"}, "box": {"title": "Box Style", "list": ["Border", "Shadow"]}, "container": {"title": "Container <PERSON><PERSON><PERSON>", "list": ["Full", "Boxed"]}, "basics": {"title": "Basic Config", "list": {"multiTab": "Show work tab", "accordion": "Sidebar opens accordion", "collapseSidebar": "Show sidebar button", "reloadPage": "Show reload page button", "breadcrumb": "Show crumb navigation", "language": "Show multilingual selection", "progressBar": "Show top progress bar", "weakMode": "Color Weakness Mode", "watermark": "Global watermark", "menuWidth": "Menu width", "tabStyle": "Tab style", "pageTransition": "Page animation", "borderRadius": "Custom radius"}}, "tabStyle": {"default": "<PERSON><PERSON><PERSON>", "card": "Card", "google": "Chrome"}, "transition": {"list": {"none": "None", "fade": "Fade", "slideLeft": "Slide Left", "slideBottom": "Slide Bottom", "slideTop": "Slide Top"}}}, "notice": {"title": "Notice", "btnRead": "<PERSON> as read", "bar": ["Notice", "Message", "Todo"], "text": ["No"], "viewAll": "View all"}, "worktab": {"btn": {"refresh": "Refresh", "fixed": "Fixed", "unfixed": "Unfixed", "closeLeft": "Close left", "closeRight": "Close right", "closeOther": "Close other", "closeAll": "Close all"}}, "login": {"leftView": {"title": "Professional Hotel Management System", "subTitle": "Comprehensive hotel booking, management and operation solutions"}, "title": "Welcome back", "subTitle": "Please enter your account and password to login", "roles": {"super": "Super Admin", "admin": "Admin", "user": "User"}, "placeholder": ["Please enter your account", "Please enter your password", "Please slide to verify"], "sliderText": "Please slide to verify", "sliderSuccessText": "Verification successful", "rememberPwd": "Remember password", "forgetPwd": "Forgot password", "btnText": "<PERSON><PERSON>", "noAccount": "No account yet?", "register": "Register", "success": {"title": "Login successful", "message": "Welcome back"}}, "forgetPassword": {"title": "Forgot password?", "subTitle": "Enter your email to reset your password", "placeholder": "Please enter your email", "submitBtnText": "Submit", "backBtnText": "Back"}, "register": {"title": "Create account", "subTitle": "Welcome to join us, please fill in the following information to complete the registration", "placeholder": ["Please enter your account", "Please enter your password", "Please enter your password again"], "rule": ["Please enter your password again", "The two passwords are inconsistent!", "The length is 3 to 20 characters", "The password length cannot be less than 6 digits", "Please agree to the privacy policy"], "agreeText": "I agree", "privacyPolicy": "Privacy policy", "submitBtnText": "Register", "hasAccount": "Already have an account?", "toLogin": "To login"}, "lockScreen": {"pwdError": "Password error", "lock": {"inputPlaceholder": "Please input lock screen password", "btnText": "Lock"}, "unlock": {"inputPlaceholder": "Please input unlock password", "btnText": "Unlock", "backBtnText": "Back to login"}}, "greeting": {"dawn": "Good morning!", "morning": "Good morning!", "afternoon": "Good afternoon!", "evening": "Good evening!"}, "exceptionPage": {"gohome": "Go Home", "403": "Sorry, you do not have permission to access this page", "404": "Sorry, the page you are trying to access does not exist", "500": "Sorry, there was an error on the server"}, "menus": {"dashboard": {"title": "Dashboard", "workspace": "Workspace"}, "booking": {"title": "Booking", "hotelSearch": "Hotel Search", "orders": "Orders", "analytics": "Booking Analytics", "search": {"title": "Hotel Search", "description": "Search hotels worldwide, find the perfect accommodation", "destination": "Destination", "destinationPlaceholder": "Enter city or hotel name", "checkIn": "Check-in", "checkOut": "Check-out", "selectDate": "Select Date", "guests": "Guests", "adults": "Adults", "children": "Children", "rooms": "Rooms", "availableOnly": "Available Suppliers Only", "selectGuests": "Select number of guests", "supplierHint": "Only show hotels from suppliers with complete data integration", "rating5Star": "5 Star", "rating4Star": "4 Star", "rating3Star": "3 Star", "rating2Star": "2 Star & Below", "searchButton": "Search Hotels", "results": "Search Results", "found": "Found", "hotels": "hotels", "priceRange": "Price Range", "hotelRating": "Hotel Rating", "sortBy": "Sort By", "applyFilters": "Apply Filters", "viewDetails": "View Details", "bookNow": "Book Now", "lowestPrice": "Lowest Price", "perNight": "/ night", "includingTaxes": "Including taxes", "images": "images", "rating": "rating", "sortOptions": {"priceLowToHigh": "Price: Low to High", "priceHighToLow": "Price: High to Low", "ratingHighToLow": "Rating: High to Low", "distanceNearest": "Distance: Nearest"}, "noResults": "No hotels found matching your criteria", "searchAgain": "Search Again", "ratingTemplate": "{value} rating", "more": "more"}, "hotel": {"lowestPrice": "Lowest Price", "perNight": "/ night", "includingTaxes": "Including taxes", "viewDetails": "View Details", "bookNow": "Book Now", "freeWifi": "Free WiFi", "freeBreakfast": "Free Breakfast", "freeCancellation": "Free Cancellation"}}, "user": {"title": "User Management", "tenantUsers": "Tenant Users", "customerUsers": "Customer Users"}, "entity": {"title": "Entity Management", "entityList": "Entity List", "entitySettings": "<PERSON><PERSON><PERSON> Settings"}, "setting": {"title": "System Management", "userCenter": "User Center", "systemSettings": "System Settings"}}, "booking": {"search": {"title": "Hotel Search", "description": "Search hotels worldwide, find the perfect accommodation", "destination": "Destination", "destinationPlaceholder": "Enter city or hotel name", "citySearchPlaceholder": "Enter destination city", "recentSearch": "Recent Search", "citySearchFailed": "City search failed, please try again", "checkIn": "Check-in", "checkOut": "Check-out", "selectDate": "Select Date", "selectGuests": "Select Guests", "guests": "Guests", "adults": "Adults", "children": "Children", "rooms": "Rooms", "currency": "<PERSON><PERSON><PERSON><PERSON>", "language": "Language", "searchButton": "Search Hotels", "reset": "Reset", "onlyAvailableSuppliers": "Available Suppliers Only", "allSuppliers": "All Suppliers", "supplierHint": "Show only hotels with complete data integration", "priceRange": "Price Range", "rating": "Hotel Rating", "sortBy": "Sort By", "applyFilters": "Apply Filters", "noResults": "No hotels found matching your criteria", "searchAgain": "Search Again", "resultsFound": "Found {count} hotels", "loading": "Searching...", "validation": {"checkInRequired": "Please select check-in date", "checkOutRequired": "Please select check-out date"}, "guestText": {"adult": "adult", "adults": "adults", "child": "child", "children": "children", "room": "room", "rooms": "rooms"}}, "hotel": {"backToSearch": "Back to Search", "hotelIntroduction": "Hotel Introduction", "hotelDescription": "Hotel Description", "hotelFacilities": "Hotel Facilities", "roomsAndPrices": "Rooms & Prices", "noRoomsAvailable": "No rooms available", "locationMap": "Location Map", "hotelLocation": "Hotel Location", "coordinates": "Coordinates", "bookingInfo": "Booking Information", "checkInDate": "Check-in Date", "checkOutDate": "Check-out Date", "stayDuration": "Stay Duration", "nights": "nights", "guestInfo": "Guest Information", "adults": "adults", "children": "children", "roomCount": "Room Count", "rooms": "rooms", "roomQuantity": "Room Quantity", "roomQuantityHint": "Max 5 rooms", "roomQuantityUpdated": "Room quantity updated", "roomType": "Room Type", "ratePlan": "Rate Plan", "perRoom": "Per Room", "totalPrice": "Total Price", "mealType": {"none": "No Meals", "breakfast": "Breakfast Included", "lunch": "Lunch Included", "dinner": "Dinner Included", "breakfastLunch": "Breakfast & Lunch", "breakfastDinner": "Breakfast & Dinner", "lunchDinner": "Lunch & Dinner", "all": "All Meals Included"}, "maxOccupancy": "Max Occupancy", "people": "people", "perNight": "/ night", "selectRoom": "Select Room", "soldOut": "Sold Out", "selectedRoom": "Selected Room", "roomFee": "Room Fee", "total": "Total", "bookNow": "Book Now", "pleaseSelectRoom": "Please select a room to continue booking", "selectedMessage": "Selected", "loadHotelDetailFailed": "Failed to load hotel details", "loadRatesFailed": "Failed to load room rates", "editDates": "Edit Dates", "confirmDates": "Confirm", "cancelEdit": "Cancel", "updatingRates": "Updating room rates...", "datesUpdated": "Dates updated", "missingHotelId": "Missing hotel ID parameter", "hotelDetailLoadFailed": "Hotel details load failed", "unknownHotel": "Unknown Hotel", "noDescription": "No hotel description available", "retryLater": "Unable to load hotel details, please try again later", "defaultAmenities": {"wifi": "Free WiFi", "pool": "Swimming Pool", "gym": "Fitness Center", "restaurant": "Restaurant", "parking": "Parking"}}, "order": {"title": "Order Management", "orderId": "Order ID", "hotelName": "Hotel Name", "guestName": "Guest Name", "checkIn": "Check-in", "checkOut": "Check-out", "totalAmount": "Total Amount", "status": "Status", "createTime": "Created", "actions": "Actions", "viewDetails": "View Details", "cancel": "Cancel Order", "confirmed": "Confirmed", "pending": "Pending", "cancelled": "Cancelled", "completed": "Completed", "loadFailed": "Failed to load order list", "loadDetailFailed": "Failed to load order details", "orderIdRequired": "Order ID is required", "confirmationNumber": "Confirmation Number", "close": "Close", "print": "Print Order", "orderDetail": "Order Details", "cancelConfirm": "Are you sure you want to cancel this order?", "cancelSuccess": "Order cancelled successfully", "cancelFailed": "Failed to cancel order"}, "booking": {"title": "Booking Confirmation", "guestInfo": "Guest Information", "firstName": "First Name", "lastName": "Last Name", "email": "Email", "phone": "Phone", "specialRequests": "Special Requests", "paymentInfo": "Payment Information", "cardNumber": "Card Number", "expiryDate": "Expiry Date", "cvv": "CVV", "cardholderName": "Cardholder Name", "confirmBooking": "Confirm Booking", "bookingSuccess": "Booking Successful", "bookingFailed": "Booking Failed", "terms": "Terms and Conditions", "privacy": "Privacy Policy", "agreeTerms": "I agree to the terms and conditions"}}, "table": {"searchBar": {"reset": "Reset", "search": "Search", "expand": "Expand", "collapse": "Collapse", "searchInputPlaceholder": "Please enter", "searchSelectPlaceholder": "Please select"}, "selection": "Select", "sizeOptions": {"small": "Compact", "default": "<PERSON><PERSON><PERSON>", "large": "Loose"}, "column": {"selection": "Select", "expand": "Expand", "index": "Index"}, "zebra": "Zebra", "border": "Border", "headerBackground": "Header B<PERSON>"}}