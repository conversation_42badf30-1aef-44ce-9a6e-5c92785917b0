import { RoutesAlias, ComponentPaths } from '../routesAlias'
import { AppRouteRecord } from '@/types/router'
import { WEB_LINKS } from '@/utils/constants'

/**
 * 菜单列表、异步路由
 *
 * 支持两种模式:
 * 前端静态配置 - 直接使用本文件中定义的路由配置
 * 后端动态配置 - 后端返回菜单数据，前端解析生成路由
 *
 * 菜单标题（title）:
 * 可以是 i18n 的 key，也可以是字符串，比如：'用户列表'
 *
 * RoutesAlias.Home 指向的是布局组件，后端返回的菜单数据中，component 字段需要指向 /index/index
 * 路由元数据（meta）：异步路由在 asyncRoutes 中配置，静态路由在 staticRoutes 中配置
 */
export const asyncRoutes: AppRouteRecord[] = [
  // Booking Module - Hotel OTA System
  {
    name: 'Booking',
    path: '/booking',
    component: ComponentPaths.Home,
    meta: {
      title: 'menus.booking.title',
      icon: '&#xe721;',
      roles: ['R_SUPER', 'R_ADMIN', 'R_USER']
    },
    children: [
      {
        path: '',
        name: 'BookingDashboard',
        component: () => import('@/views/booking/index.vue'),
        meta: {
          title: 'menus.booking.dashboard',
          keepAlive: true,
          fixedTab: true
        }
      },
      {
        path: 'hotel-search',
        name: 'HotelSearch',
        component: () => import('@/views/booking/hotel-search/index.vue'),
        meta: {
          title: 'menus.booking.hotelSearch',
          keepAlive: true
        }
      },
      {
        path: 'hotel-search/detail',
        name: 'HotelDetail',
        component: () => import('@/views/booking/hotel-search/hotel-detail.vue'),
        meta: {
          title: 'menus.booking.hotelDetail',
          isHide: true,
          keepAlive: true,
          activePath: '/booking/hotel-search'
        }
      },
      {
        path: 'hotel-search/book',
        name: 'HotelBook',
        component: () => import('@/views/booking/hotel-search/hotel-book.vue'),
        meta: {
          title: 'menus.booking.hotelBook',
          isHide: true,
          keepAlive: false,
          activePath: '/booking/hotel-search'
        }
      },
      {
        path: 'orders',
        name: 'Orders',
        component: () => import('@/views/booking/order-management/index.vue'),
        meta: {
          title: 'menus.booking.orders',
          keepAlive: true
        }
      },
      {
        path: 'orders/detail/:orderId',
        name: 'OrderDetail',
        component: () => import('@/views/booking/order-management/order-detail.vue'),
        meta: {
          title: 'menus.booking.orderDetail',
          isHide: true,
          keepAlive: false,
          activePath: '/booking/orders'
        }
      },
      {
        path: 'analytics',
        name: 'BookingAnalytics',
        component: () => import('@/views/booking/analytics/index.vue'),
        meta: {
          title: 'menus.booking.analytics',
          keepAlive: true
        }
      }
    ]
  },
  {
    name: 'Dashboard',
    path: '/dashboard',
    component: ComponentPaths.Home,
    meta: {
      title: 'menus.dashboard.title',
      icon: '&#xe721;',
      roles: ['R_SUPER', 'R_ADMIN'] // 角色权限，前端控制模式（只有拥有这些角色的用户才能访问）
    },
    children: [
      {
        path: 'console',
        name: 'Console',
        component: ComponentPaths.Dashboard,
        meta: {
          title: 'menus.dashboard.console',
          keepAlive: true,
          fixedTab: true
        }
      },
      {
        path: 'analysis',
        name: 'Analysis',
        component: ComponentPaths.Analysis,
        meta: {
          title: 'menus.dashboard.analysis',
          keepAlive: false
        }
      },
      {
        path: 'ecommerce',
        name: 'Ecommerce',
        component: ComponentPaths.Ecommerce,
        meta: {
          title: 'menus.dashboard.ecommerce',
          keepAlive: false
        }
      }
    ]
  },
  {
    path: '/template',
    name: 'Template',
    component: ComponentPaths.Home,
    meta: {
      title: 'menus.template.title',
      icon: '&#xe860;'
    },
    children: [
      {
        path: 'cards',
        name: 'Cards',
        component: ComponentPaths.Cards,
        meta: {
          title: 'menus.template.cards',
          keepAlive: false
        }
      },
      {
        path: 'banners',
        name: 'Banners',
        component: ComponentPaths.Banners,
        meta: {
          title: 'menus.template.banners',
          keepAlive: false
        }
      },
      {
        path: 'charts',
        name: 'Charts',
        component: ComponentPaths.Charts,
        meta: {
          title: 'menus.template.charts',
          keepAlive: false
        }
      },
      {
        path: 'map',
        name: 'Map',
        component: ComponentPaths.Map,
        meta: {
          title: 'menus.template.map',
          keepAlive: true
        }
      },
      {
        path: 'chat',
        name: 'Chat',
        component: ComponentPaths.Chat,
        meta: {
          title: 'menus.template.chat',
          keepAlive: true
        }
      },
      {
        path: 'calendar',
        name: 'Calendar',
        component: ComponentPaths.Calendar,
        meta: {
          title: 'menus.template.calendar',
          keepAlive: true
        }
      },
      {
        path: 'pricing',
        name: 'Pricing',
        component: ComponentPaths.Pricing,
        meta: {
          title: 'menus.template.pricing',
          keepAlive: true,
          isFullPage: true // 是否全屏显示
        }
      }
    ]
  },
  {
    path: '/widgets',
    name: 'Widgets',
    component: ComponentPaths.Home,
    meta: {
      title: 'menus.widgets.title',
      icon: '&#xe81a;'
    },
    children: [
      {
        path: 'icon-list',
        name: 'IconList',
        component: ComponentPaths.IconList,
        meta: {
          title: 'menus.widgets.iconList',
          keepAlive: true
        }
      },
      {
        path: 'icon-selector',
        name: 'IconSelector',
        component: ComponentPaths.IconSelector,
        meta: {
          title: 'menus.widgets.iconSelector',
          keepAlive: true
        }
      },
      {
        path: 'image-crop',
        name: 'ImageCrop',
        component: ComponentPaths.ImageCrop,
        meta: {
          title: 'menus.widgets.imageCrop',
          keepAlive: true
        }
      },
      {
        path: 'excel',
        name: 'Excel',
        component: ComponentPaths.Excel,
        meta: {
          title: 'menus.widgets.excel',
          keepAlive: true
        }
      },
      {
        path: 'video',
        name: 'Video',
        component: ComponentPaths.Video,
        meta: {
          title: 'menus.widgets.video',
          keepAlive: true
        }
      },
      {
        path: 'count-to',
        name: 'CountTo',
        component: ComponentPaths.CountTo,
        meta: {
          title: 'menus.widgets.countTo',
          keepAlive: false
        }
      },
      {
        path: 'wang-editor',
        name: 'WangEditor',
        component: ComponentPaths.WangEditor,
        meta: {
          title: 'menus.widgets.wangEditor',
          keepAlive: true
        }
      },
      {
        path: 'watermark',
        name: 'Watermark',
        component: ComponentPaths.Watermark,
        meta: {
          title: 'menus.widgets.watermark',
          keepAlive: true
        }
      },
      {
        path: 'context-menu',
        name: 'ContextMenu',
        component: ComponentPaths.ContextMenu,
        meta: {
          title: 'menus.widgets.contextMenu',
          keepAlive: true
        }
      },
      {
        path: 'qrcode',
        name: 'Qrcode',
        component: ComponentPaths.Qrcode,
        meta: {
          title: 'menus.widgets.qrcode',
          keepAlive: true
        }
      },
      {
        path: 'drag',
        name: 'Drag',
        component: ComponentPaths.Drag,
        meta: {
          title: 'menus.widgets.drag',
          keepAlive: true
        }
      },
      {
        path: 'text-scroll',
        name: 'TextScroll',
        component: ComponentPaths.TextScroll,
        meta: {
          title: 'menus.widgets.textScroll',
          keepAlive: true
        }
      },
      {
        path: 'fireworks',
        name: 'Fireworks',
        component: ComponentPaths.Fireworks,
        meta: {
          title: 'menus.widgets.fireworks',
          keepAlive: true,
          showTextBadge: 'Hot'
        }
      },
      {
        path: '/outside/iframe/elementui',
        name: 'ElementUI',
        component: '',
        meta: {
          title: 'menus.widgets.elementUI',
          keepAlive: false,
          link: 'https://element-plus.org/zh-CN/component/overview.html',
          isIframe: true,
          showBadge: true
        }
      }
    ]
  },
  {
    path: '/examples',
    name: 'Examples',
    component: ComponentPaths.Home,
    meta: {
      title: 'menus.examples.title',
      icon: '&#xe8d4;'
    },
    children: [
      {
        path: 'tabs',
        name: 'Tabs',
        component: ComponentPaths.ExamplesTabs,
        meta: {
          title: 'menus.examples.tabs',
          keepAlive: true
        }
      }
    ]
  },
  {
    path: '/system',
    name: 'System',
    component: ComponentPaths.Home,
    meta: {
      title: 'menus.system.title',
      icon: '&#xe7b9;',
      roles: ['R_SUPER', 'R_ADMIN']
    },
    children: [
      {
        path: 'user',
        name: 'User',
        component: ComponentPaths.User,
        meta: {
          title: 'menus.system.user',
          keepAlive: true,
          roles: ['R_SUPER', 'R_ADMIN']
        }
      },
      {
        path: 'role',
        name: 'Role',
        component: ComponentPaths.Role,
        meta: {
          title: 'menus.system.role',
          keepAlive: true,
          roles: ['R_SUPER']
        }
      },
      {
        path: 'user-center',
        name: 'UserCenter',
        component: ComponentPaths.UserCenter,
        meta: {
          title: 'menus.system.userCenter',
          keepAlive: true
        }
      },
      {
        path: 'menu',
        name: 'Menus',
        component: ComponentPaths.Menu,
        meta: {
          title: 'menus.system.menu',
          keepAlive: true,
          roles: ['R_SUPER'],
          authList: [
            {
              title: '新增',
              auth_mark: 'add'
            },
            {
              title: '编辑',
              auth_mark: 'edit'
            },
            {
              title: '删除',
              auth_mark: 'delete'
            }
          ]
        }
      },
      {
        path: 'starling',
        name: 'Starling',
        component: ComponentPaths.Starling,
        meta: {
          title: 'menus.system.starling',
          keepAlive: true,
          roles: ['R_SUPER', 'R_ADMIN']
        }
      },
      {
        path: 'nested',
        name: 'Nested',
        component: '',
        meta: {
          title: 'menus.system.nested',
          keepAlive: true
        },
        children: [
          {
            path: 'menu1',
            name: 'NestedMenu1',
            component: ComponentPaths.NestedMenu1,
            meta: {
              title: 'menus.system.menu1',
              icon: '&#xe676;',
              keepAlive: true
            }
          },
          {
            path: 'menu2',
            name: 'NestedMenu2',
            component: '',
            meta: {
              title: 'menus.system.menu2',
              icon: '&#xe676;',
              keepAlive: true
            },
            children: [
              {
                path: 'menu2-1',
                name: 'NestedMenu2-1',
                component: ComponentPaths.NestedMenu21,
                meta: {
                  title: 'menus.system.menu21',
                  icon: '&#xe676;',
                  keepAlive: true
                }
              }
            ]
          },
          {
            path: 'menu3',
            name: 'NestedMenu3',
            component: '',
            meta: {
              title: 'menus.system.menu3',
              icon: '&#xe676;',
              keepAlive: true
            },
            children: [
              {
                path: 'menu3-1',
                name: 'NestedMenu3-1',
                component: ComponentPaths.NestedMenu31,
                meta: {
                  title: 'menus.system.menu31',
                  icon: '&#xe676;',
                  keepAlive: true
                }
              },
              {
                path: 'menu3-2',
                name: 'NestedMenu3-2',
                component: '',
                meta: {
                  title: 'menus.system.menu32',
                  icon: '&#xe676;',
                  keepAlive: true
                },
                children: [
                  {
                    path: 'menu3-2-1',
                    name: 'NestedMenu3-2-1',
                    component: ComponentPaths.NestedMenu321,
                    meta: {
                      title: 'menus.system.menu321',
                      icon: '&#xe676;',
                      keepAlive: true
                    }
                  }
                ]
              }
            ]
          }
        ]
      }
    ]
  },
  {
    path: '/article',
    name: 'Article',
    component: ComponentPaths.Home,
    meta: {
      title: 'menus.article.title',
      icon: '&#xe7ae;',
      roles: ['R_SUPER', 'R_ADMIN']
    },
    children: [
      {
        path: 'article-list',
        name: 'ArticleList',
        component: ComponentPaths.ArticleList,
        meta: {
          title: 'menus.article.articleList',
          keepAlive: true,
          authList: [
            {
              title: '新增',
              auth_mark: 'add'
            },
            {
              title: '编辑',
              auth_mark: 'edit'
            }
          ]
        }
      },

      {
        path: 'detail',
        name: 'ArticleDetail',
        component: ComponentPaths.ArticleDetail,
        meta: {
          title: 'menus.article.articleDetail',
          isHide: true,
          keepAlive: true,
          activePath: '/article/article-list' // 激活菜单路径
        }
      },
      {
        path: 'comment',
        name: 'ArticleComment',
        component: ComponentPaths.Comment,
        meta: {
          title: 'menus.article.comment',
          keepAlive: true
        }
      },
      {
        path: 'publish',
        name: 'ArticlePublish',
        component: ComponentPaths.ArticlePublish,
        meta: {
          title: 'menus.article.articlePublish',
          keepAlive: true,
          authList: [
            {
              title: '发布',
              auth_mark: 'article/article-publish/add'
            }
          ]
        }
      }
    ]
  },
  {
    path: '/result',
    name: 'Result',
    component: ComponentPaths.Home,
    meta: {
      title: 'menus.result.title',
      icon: '&#xe715;'
    },
    children: [
      {
        path: 'success',
        name: 'ResultSuccess',
        component: ComponentPaths.Success,
        meta: {
          title: 'menus.result.success',
          keepAlive: true
        }
      },
      {
        path: 'fail',
        name: 'ResultFail',
        component: ComponentPaths.Fail,
        meta: {
          title: 'menus.result.fail',
          keepAlive: true
        }
      }
    ]
  },
  {
    path: '/exception',
    name: 'Exception',
    component: ComponentPaths.Home,
    meta: {
      title: 'menus.exception.title',
      icon: '&#xe820;'
    },
    children: [
      {
        path: '403',
        name: '403',
        component: ComponentPaths.Exception403,
        meta: {
          title: 'menus.exception.forbidden',
          keepAlive: true
        }
      },
      {
        path: '404',
        name: '404',
        component: ComponentPaths.Exception404,
        meta: {
          title: 'menus.exception.notFound',
          keepAlive: true
        }
      },
      {
        path: '500',
        name: '500',
        component: ComponentPaths.Exception500,
        meta: {
          title: 'menus.exception.serverError',
          keepAlive: true
        }
      }
    ]
  },

  {
    path: '/safeguard',
    name: 'Safeguard',
    component: ComponentPaths.Home,
    meta: {
      title: 'menus.safeguard.title',
      icon: '&#xe816;',
      keepAlive: false
    },
    children: [
      {
        path: 'server',
        name: 'SafeguardServer',
        component: ComponentPaths.Server,
        meta: {
          title: 'menus.safeguard.server',
          keepAlive: true
        }
      }
    ]
  },
  {
    name: 'Help',
    path: '/help',
    component: ComponentPaths.Home,
    meta: {
      title: 'menus.help.title',
      icon: '&#xe719;',
      keepAlive: false,
      roles: ['R_SUPER', 'R_ADMIN']
    },
    children: [
      {
        path: '',
        name: 'Document',
        meta: {
          title: 'menus.help.document',
          link: WEB_LINKS.DOCS,
          isIframe: false,
          keepAlive: false
        }
      }
    ]
  },

  // Entity Management Module
  {
    name: 'Entity',
    path: '/entity',
    component: ComponentPaths.Home,
    meta: {
      title: 'menus.entity.title',
      icon: '&#xe7b0;',
      roles: ['R_SUPER', 'R_ADMIN']
    },
    children: [
      {
        path: 'entity-list',
        name: 'EntityList',
        component: ComponentPaths.EntityList,
        meta: {
          title: 'menus.entity.entityList',
          keepAlive: true,
          roles: ['R_SUPER', 'R_ADMIN']
        }
      },
      {
        path: 'entity-edit/:id?',
        name: 'EntityEdit',
        component: ComponentPaths.EntityEdit,
        meta: {
          title: 'menus.entity.entityEdit',
          isHide: true,
          keepAlive: true,
          activePath: '/entity/entity-list'
        }
      }
    ]
  },

  // User Management Module
  {
    name: 'Users',
    path: '/users',
    component: ComponentPaths.Home,
    meta: {
      title: 'menus.users.title',
      icon: '&#xe7b1;',
      roles: ['R_SUPER', 'R_ADMIN']
    },
    children: [
      {
        path: 'tenant-users',
        name: 'TenantUsers',
        component: ComponentPaths.TenantUsers,
        meta: {
          title: 'menus.users.tenantUsers',
          keepAlive: true,
          roles: ['R_SUPER', 'R_ADMIN']
        }
      },
      {
        path: 'customer-users',
        name: 'CustomerUsers',
        component: ComponentPaths.CustomerUsers,
        meta: {
          title: 'menus.users.customerUsers',
          keepAlive: true,
          roles: ['R_SUPER', 'R_ADMIN']
        }
      },
      {
        path: 'platform-users',
        name: 'PlatformUsers',
        component: ComponentPaths.PlatformUsers,
        meta: {
          title: 'menus.users.platformUsers',
          keepAlive: true,
          roles: ['R_SUPER']
        }
      },
      {
        path: 'user-edit/:id?',
        name: 'UserEdit',
        component: ComponentPaths.UserEdit,
        meta: {
          title: 'menus.users.userEdit',
          isHide: true,
          keepAlive: true,
          activePath: '/users/tenant-users'
        }
      }
    ]
  },

  // 一级菜单
  {
    name: 'ChangeLog',
    path: '/change/log',
    component: ComponentPaths.ChangeLog,
    meta: {
      title: 'menus.plan.log',
      showTextBadge: `v${__APP_VERSION__}`,
      icon: '&#xe712;',
      keepAlive: false
    }
  }
]
