import request from '@/utils/http'
import type { AppRouteRecord } from '@/types/router'
import { internalHomepageCache } from '@/utils/cache/internalHomepageCache'

interface MenuRouteResponse {
  menuRoutes: MenuRoute[]
  starlingMap: Record<string, any>
  downloadButtonURLs: Record<string, string>
}

interface MenuRoute {
  id: string
  path: string
  component: string
  name: string
  title: {
    zh: string
    en: string
    ar?: string
  }
  icon?: string
  meta?: {
    requiresAuth?: boolean
    roles?: string[]
    permissions?: string[]
    keepAlive?: boolean
    isHide?: boolean
    activePath?: string
  }
  children?: MenuRoute[]
}

// 新版本菜单API - 调用 InternalHomepage 接口
export const menuApiV2 = {
  /**
   * 获取内部管理菜单列表
   * 调用后端 /api/view/internalHomepage 接口
   * 带有10分钟缓存机制，避免频繁请求
   */
  async getInternalMenuList(): Promise<{ menuList: AppRouteRecord[] }> {
    try {
      // 1. 尝试从缓存获取数据
      const cachedData = internalHomepageCache.getCachedInternalHomepage()
      if (cachedData) {
        console.log('[MenuAPI] 使用缓存数据，避免重复请求')
        const menuRoutes = cachedData.menuRoutes || []
        const convertedMenuList = this.convertToAppRouteRecord(menuRoutes)
        return { menuList: convertedMenuList }
      }

      // 2. 缓存未命中，请求后端接口
      console.log('[MenuAPI] 缓存未命中，请求后端接口')
      const response = await request.post<MenuRouteResponse>({
        url: '/api/view/internalHomepage',
        data: {}
      })

      // 3. 缓存响应数据
      const responseData = {
        menuRoutes: response.menuRoutes || [],
        starlingMap: response.starlingMap || {},
        downloadButtonURLs: response.downloadButtonURLs || {}
      }
      internalHomepageCache.setCachedInternalHomepage(responseData)

      // HTTP拦截器已经将data提取到顶层，直接访问menuRoutes
      const menuRoutes = response.menuRoutes || []
      console.log('Raw menuRoutes from backend:', menuRoutes)

      const convertedMenuList = this.convertToAppRouteRecord(menuRoutes)
      console.log('Converted menuList:', convertedMenuList)

      return {
        menuList: convertedMenuList
      }
    } catch (error) {
      console.error('获取内部菜单失败:', error)
      throw error instanceof Error ? error : new Error('获取菜单失败')
    }
  },

  /**
   * 转换 MenuRoute 为 AppRouteRecord
   * 提取为独立方法，便于复用
   */
  convertToAppRouteRecord(routes: MenuRoute[], parentPath = ''): AppRouteRecord[] {
    return routes.map(route => {
      // 构建完整路径：如果是绝对路径则直接使用，否则与父路径拼接
      let fullPath = route.path
      if (parentPath && !route.path.startsWith('/')) {
        fullPath = `${parentPath}/${route.path}`.replace(/\/+/g, '/')
      }

      const appRoute: AppRouteRecord = {
        path: fullPath,
        name: route.name,
        component: route.component,
        meta: {
          title: route.title || route.name, // 保持完整的 i18n.I18N 格式
          icon: route.icon,
          keepAlive: route.meta?.keepAlive,
          roles: route.meta?.roles,
          permissions: route.meta?.permissions,
          isHide: route.meta?.isHide,
          activePath: route.meta?.activePath
        }
      }

      console.log('Converting route:', {
        name: route.name,
        title: route.title,
        appRoute: appRoute
      })

      if (route.children && route.children.length > 0) {
        appRoute.children = this.convertToAppRouteRecord(route.children, fullPath)
      }

      return appRoute
    })
  },

  /**
   * 获取starling多语言映射
   * 优先使用缓存，避免重复请求
   */
  async getStarlingMap(): Promise<Record<string, any>> {
    try {
      // 1. 尝试从缓存获取数据
      const cachedData = internalHomepageCache.getCachedInternalHomepage()
      if (cachedData && cachedData.starlingMap) {
        console.log('[MenuAPI] 使用缓存的starlingMap')
        return cachedData.starlingMap
      }

      // 2. 缓存未命中，请求后端接口
      console.log('[MenuAPI] starlingMap缓存未命中，请求后端接口')
      const response = await request.post<MenuRouteResponse>({
        url: '/api/view/internalHomepage',
        data: {}
      })

      // 3. 缓存响应数据
      const responseData = {
        menuRoutes: response.menuRoutes || [],
        starlingMap: response.starlingMap || {},
        downloadButtonURLs: response.downloadButtonURLs || {}
      }
      internalHomepageCache.setCachedInternalHomepage(responseData)

      return response.starlingMap || {}
    } catch (error) {
      console.error('获取多语言映射失败:', error)
      return {}
    }
  },

  /**
   * 强制刷新缓存
   * 清除当前缓存并重新请求数据
   */
  async forceRefresh(): Promise<{ menuList: AppRouteRecord[] }> {
    console.log('[MenuAPI] 强制刷新，清除缓存')
    internalHomepageCache.clearInternalHomepageCache()
    return await this.getInternalMenuList()
  },

  /**
   * 获取缓存统计信息
   */
  getCacheStats() {
    return internalHomepageCache.getCacheStats()
  }
}

// 兼容旧的 menuService 导出名称
export const menuService = {
  async getMenuList(): Promise<{ menuList: AppRouteRecord[] }> {
    return await menuApiV2.getInternalMenuList()
  }
}
