import request from '@/utils/http'
import type { BffTable } from '@/types/bff'

// Starling多语言配置相关类型定义
export interface StarlingItem {
  id: number
  entityId: number
  application: string
  space: string
  key: string
  content: {
    zh: string
    en: string
    ar?: string
  }
  createTime: string
  updateTime: string
}

export interface ListStarlingRequest {
  entityId?: number
  application?: string
  space?: string
  key?: string
  keyword?: string
  page?: {
    pageNum: number
    pageSize: number
  }
}

export interface CreateStarlingRequest {
  entityId: number
  application: string
  space: string
  key: string
  content: {
    zh: string
    en: string
    ar?: string
  }
}

export interface UpdateStarlingRequest {
  id: number
  entityId: number
  application: string
  space: string
  key: string
  content: {
    zh: string
    en: string
    ar?: string
  }
}

// Starling多语言配置API服务
export const starlingApi = {
  /**
   * 获取多语言配置列表
   */
  async listStarling(params: ListStarlingRequest): Promise<BffTable<StarlingItem>> {
    const response = await request.post<{ table: BffTable<StarlingItem> }>({
      url: '/api/content/listStarling',
      data: params
    })
    // axios拦截器已经返回了data字段，不需要再访问.data
    return response.table
  },

  /**
   * 获取单个多语言配置
   */
  async getStarling(id: number): Promise<StarlingItem> {
    const response = await request.post<{ item: StarlingItem }>({
      url: '/api/content/getStarling',
      data: { id }
    })
    // axios拦截器已经返回了data字段，不需要再访问.data
    return response.item
  },

  /**
   * 创建多语言配置
   */
  async createStarling(data: CreateStarlingRequest): Promise<{ id: number }> {
    const response = await request.post<{ id: number }>({
      url: '/api/content/createStarling',
      data
    })
    return response.data
  },

  /**
   * 更新多语言配置
   */
  async updateStarling(data: UpdateStarlingRequest): Promise<void> {
    await request.post({
      url: '/api/content/updateStarling',
      data
    })
  },

  /**
   * 删除多语言配置
   */
  async deleteStarling(id: number): Promise<void> {
    await request.post({
      url: '/api/content/deleteStarling',
      data: { id }
    })
  },

  /**
   * 批量删除多语言配置
   */
  async batchDeleteStarling(ids: number[]): Promise<void> {
    await request.post({
      url: '/api/content/batchDeleteStarling',
      data: { ids }
    })
  },

  /**
   * 导出多语言配置
   */
  async exportStarling(params: {
    entityId?: number
    application?: string
    space?: string
  }): Promise<{ fileUrl: string }> {
    const response = await request.post<{ fileUrl: string }>({
      url: '/api/content/exportStarling',
      data: params
    })
    return response.data
  },

  /**
   * 导入多语言配置
   */
  async importStarling(data: {
    fileUrl: string
    replace?: boolean
  }): Promise<{
    success: boolean
    importCount: number
    updateCount: number
  }> {
    const response = await request.post<{
      success: boolean
      importCount: number
      updateCount: number
    }>({
      url: '/api/content/importStarling',
      data
    })
    return response.data
  },

  /**
   * 获取应用列表
   */
  async getApplicationList(entityId?: number): Promise<string[]> {
    const response = await request.post<{ applications: string[] }>({
      url: '/api/content/getApplicationList',
      data: { entityId }
    })
    return response.data.applications
  },

  /**
   * 获取空间列表
   */
  async getSpaceList(params: {
    entityId?: number
    application?: string
  }): Promise<string[]> {
    const response = await request.post<{ spaces: string[] }>({
      url: '/api/content/getSpaceList',
      data: params
    })
    return response.data.spaces
  }
}
