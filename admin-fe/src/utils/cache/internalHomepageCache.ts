/**
 * InternalHomepage接口缓存管理
 * 缓存10分钟，避免频繁请求后端接口
 */

import { AppRouteRecord } from '@/types/router'
import { setGlobalStarlingMap } from '@/utils/i18n'

// 缓存接口
interface CacheItem<T> {
  data: T
  timestamp: number
  expireTime: number
}

// InternalHomepage响应数据结构
interface InternalHomepageData {
  menuRoutes: any[]
  starlingMap: Record<string, any>
  downloadButtonURLs: Record<string, string>
}

// 缓存配置
const CACHE_CONFIG = {
  // 缓存时间：10分钟
  CACHE_DURATION: 10 * 60 * 1000,
  // 缓存键名
  CACHE_KEY: 'internal_homepage_cache',
  // 强制刷新标识键名
  FORCE_REFRESH_KEY: 'internal_homepage_force_refresh'
}

class InternalHomepageCache {
  private cache: Map<string, CacheItem<any>> = new Map()

  /**
   * 设置缓存
   * @param key 缓存键
   * @param data 缓存数据
   * @param duration 缓存时长（毫秒），默认10分钟
   */
  private setCache<T>(key: string, data: T, duration: number = CACHE_CONFIG.CACHE_DURATION): void {
    const now = Date.now()
    const cacheItem: CacheItem<T> = {
      data,
      timestamp: now,
      expireTime: now + duration
    }
    
    this.cache.set(key, cacheItem)
    console.log(`[Cache] 设置缓存: ${key}, 过期时间: ${new Date(cacheItem.expireTime).toLocaleString()}`)
  }

  /**
   * 获取缓存
   * @param key 缓存键
   * @returns 缓存数据或null
   */
  private getCache<T>(key: string): T | null {
    const cacheItem = this.cache.get(key)
    
    if (!cacheItem) {
      console.log(`[Cache] 缓存不存在: ${key}`)
      return null
    }

    const now = Date.now()
    if (now > cacheItem.expireTime) {
      console.log(`[Cache] 缓存已过期: ${key}`)
      this.cache.delete(key)
      return null
    }

    const remainingTime = Math.round((cacheItem.expireTime - now) / 1000)
    console.log(`[Cache] 命中缓存: ${key}, 剩余时间: ${remainingTime}秒`)
    return cacheItem.data as T
  }

  /**
   * 清除指定缓存
   * @param key 缓存键
   */
  private clearCache(key: string): void {
    this.cache.delete(key)
    console.log(`[Cache] 清除缓存: ${key}`)
  }

  /**
   * 清除所有缓存
   */
  private clearAllCache(): void {
    this.cache.clear()
    console.log('[Cache] 清除所有缓存')
  }

  /**
   * 检查是否需要强制刷新
   * @returns 是否需要强制刷新
   */
  private shouldForceRefresh(): boolean {
    const forceRefresh = sessionStorage.getItem(CACHE_CONFIG.FORCE_REFRESH_KEY)
    if (forceRefresh === 'true') {
      sessionStorage.removeItem(CACHE_CONFIG.FORCE_REFRESH_KEY)
      console.log('[Cache] 检测到强制刷新标识，清除缓存')
      return true
    }
    return false
  }

  /**
   * 设置强制刷新标识
   * 用于首页刷新时清除缓存
   */
  public setForceRefresh(): void {
    sessionStorage.setItem(CACHE_CONFIG.FORCE_REFRESH_KEY, 'true')
    console.log('[Cache] 设置强制刷新标识')
  }

  /**
   * 获取InternalHomepage缓存数据
   * @returns 缓存的数据或null
   */
  public getCachedInternalHomepage(): InternalHomepageData | null {
    // 检查是否需要强制刷新
    if (this.shouldForceRefresh()) {
      this.clearCache(CACHE_CONFIG.CACHE_KEY)
      return null
    }

    return this.getCache<InternalHomepageData>(CACHE_CONFIG.CACHE_KEY)
  }

  /**
   * 设置InternalHomepage缓存数据
   * @param data 要缓存的数据
   */
  public setCachedInternalHomepage(data: InternalHomepageData): void {
    this.setCache(CACHE_CONFIG.CACHE_KEY, data)
    
    // 同时更新全局starlingMap
    if (data.starlingMap) {
      setGlobalStarlingMap(data.starlingMap)
    }
  }

  /**
   * 清除InternalHomepage缓存
   */
  public clearInternalHomepageCache(): void {
    this.clearCache(CACHE_CONFIG.CACHE_KEY)
  }

  /**
   * 获取缓存统计信息
   */
  public getCacheStats(): {
    totalCaches: number
    internalHomepageCache: {
      exists: boolean
      timestamp?: string
      expireTime?: string
      remainingSeconds?: number
    }
  } {
    const internalHomepageCache = this.cache.get(CACHE_CONFIG.CACHE_KEY)
    const now = Date.now()

    return {
      totalCaches: this.cache.size,
      internalHomepageCache: {
        exists: !!internalHomepageCache,
        timestamp: internalHomepageCache ? new Date(internalHomepageCache.timestamp).toLocaleString() : undefined,
        expireTime: internalHomepageCache ? new Date(internalHomepageCache.expireTime).toLocaleString() : undefined,
        remainingSeconds: internalHomepageCache ? Math.round((internalHomepageCache.expireTime - now) / 1000) : undefined
      }
    }
  }

  /**
   * 检查缓存是否有效
   * @returns 缓存是否有效
   */
  public isCacheValid(): boolean {
    const cached = this.getCachedInternalHomepage()
    return cached !== null
  }
}

// 创建单例实例
export const internalHomepageCache = new InternalHomepageCache()

// 监听页面刷新事件
window.addEventListener('beforeunload', () => {
  // 如果是首页刷新，设置强制刷新标识
  if (window.location.hash === '#/' || window.location.hash === '') {
    internalHomepageCache.setForceRefresh()
  }
})

// 监听页面可见性变化，长时间不活跃后清除缓存
let lastActiveTime = Date.now()
document.addEventListener('visibilitychange', () => {
  if (document.visibilityState === 'visible') {
    const now = Date.now()
    // 如果页面隐藏超过30分钟，清除缓存
    if (now - lastActiveTime > 30 * 60 * 1000) {
      console.log('[Cache] 页面长时间不活跃，清除缓存')
      internalHomepageCache.clearInternalHomepageCache()
    }
    lastActiveTime = now
  }
})

export default internalHomepageCache
